package corp.jamaro.jamaroescritoriofx.appfx.producto.service;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Servicio para agrupar atributos por grupos y manejar la lógica de creación
 * de atributos automáticos cuando se agregan grupos a productos nuevos.
 */
@Service
@Slf4j
public class AtributoGroupingService {

    /**
     * Clase interna para almacenar información optimizada del grupo
     */
    private static class GrupoInfo {
        final String nombreGrupo;
        final Integer orden;

        GrupoInfo(String nombreGrupo, Integer orden) {
            this.nombreGrupo = nombreGrupo;
            this.orden = orden;
        }
    }
    
    /**
     * Agrupa los atributos existentes por grupos basándose en los filtros comunes.
     * Los atributos que no coincidan con ningún grupo van a "Extras".
     * Los atributos se ordenan según el orden definido en GrupoFiltroRelation.
     */
    public Map<String, List<Atributo>> groupExistingAtributos(
            Set<Atributo> atributos,
            Set<Grupo> grupos) {

        Map<String, List<Atributo>> groupedAtributos = new LinkedHashMap<>();
        List<Atributo> extraAtributos = new ArrayList<>();

        // Crear mapas optimizados para búsqueda y ordenamiento
        Map<UUID, GrupoInfo> filtroToGrupoInfoMap = createOptimizedFiltroToGrupoMap(grupos);

        // Agrupar atributos existentes
        for (Atributo atributo : atributos) {
            if (atributo.getFiltro() != null && atributo.getFiltro().getId() != null) {
                GrupoInfo grupoInfo = filtroToGrupoInfoMap.get(atributo.getFiltro().getId());

                if (grupoInfo != null) {
                    groupedAtributos.computeIfAbsent(grupoInfo.nombreGrupo, k -> new ArrayList<>()).add(atributo);
                } else {
                    extraAtributos.add(atributo);
                }
            } else {
                extraAtributos.add(atributo);
            }
        }

        // Ordenar atributos dentro de cada grupo usando el mapa optimizado
        for (Map.Entry<String, List<Atributo>> entry : groupedAtributos.entrySet()) {
            String grupoNombre = entry.getKey();
            List<Atributo> atributosDelGrupo = entry.getValue();

            if (!"Extras".equals(grupoNombre)) {
                // Ordenar usando la información precalculada
                atributosDelGrupo.sort((a1, a2) -> {
                    GrupoInfo info1 = filtroToGrupoInfoMap.get(a1.getFiltro().getId());
                    GrupoInfo info2 = filtroToGrupoInfoMap.get(a2.getFiltro().getId());

                    Integer orden1 = info1 != null ? info1.orden : Integer.MAX_VALUE;
                    Integer orden2 = info2 != null ? info2.orden : Integer.MAX_VALUE;

                    return Integer.compare(orden1, orden2);
                });

                log.debug("Ordenados {} atributos para grupo: {}", atributosDelGrupo.size(), grupoNombre);
            }
        }

        // Agregar extras si existen
        if (!extraAtributos.isEmpty()) {
            groupedAtributos.put("Extras", extraAtributos);
            log.debug("Agregados {} atributos extras", extraAtributos.size());
        }

        return groupedAtributos;
    }
    
    /**
     * Crea atributos automáticamente para un grupo cuando se agrega a un producto nuevo.
     * Los atributos se crean con UUID vacío y valores por defecto según el tipo de filtro.
     * Se mantiene el orden definido en GrupoFiltroRelation.
     */
    public List<Atributo> createAtributosForGrupo(Grupo grupo) {
        List<Atributo> nuevosAtributos = new ArrayList<>();

        if (grupo.getFiltros() == null || grupo.getFiltros().isEmpty()) {
            log.warn("Grupo {} no tiene filtros definidos", grupo.getId());
            return nuevosAtributos;
        }

        // Ordenar filtros por orden, manejando valores null
        List<GrupoFiltroRelation> filtrosOrdenados = grupo.getFiltros().stream()
                .sorted(Comparator.comparing(
                    relation -> relation.getOrden() != null ? relation.getOrden() : Integer.MAX_VALUE))
                .collect(Collectors.toList());

        // Crear atributo para cada filtro en el orden correcto
        for (GrupoFiltroRelation filtroRelation : filtrosOrdenados) {
            Filtro filtro = filtroRelation.getFiltro();

            if (filtro != null) {
                Atributo nuevoAtributo = new Atributo();
                nuevoAtributo.setId(null); // UUID vacío, el servidor lo asignará
                nuevoAtributo.setFiltro(filtro);

                // Inicializar con valores por defecto según el tipo
                initializeAtributoDefaultValue(nuevoAtributo, filtro);

                nuevosAtributos.add(nuevoAtributo);
                log.debug("Creado atributo para filtro: {} (orden: {})",
                        filtro.getNombreFiltro(), filtroRelation.getOrden());
            }
        }

        log.info("Creados {} atributos para grupo: {}",
                nuevosAtributos.size(), getGrupoPrincipalName(grupo));
        return nuevosAtributos;
    }
    
    /**
     * Actualiza los atributos existentes cuando se agregan o quitan grupos.
     * Mantiene los atributos existentes y agrega los nuevos necesarios.
     * Los nuevos atributos se crean respetando el orden del grupo.
     */
    public Set<Atributo> updateAtributosForGroups(
            Set<Atributo> atributosExistentes,
            Set<Grupo> gruposActuales) {

        Set<Atributo> atributosActualizados = new HashSet<>(atributosExistentes);

        // Crear mapa optimizado para verificación rápida
        Map<UUID, GrupoInfo> filtroToGrupoInfoMap = createOptimizedFiltroToGrupoMap(gruposActuales);

        // Obtener filtros que ya existen en los atributos
        Set<UUID> filtrosExistentes = atributosExistentes.stream()
                .filter(attr -> attr.getFiltro() != null && attr.getFiltro().getId() != null)
                .map(attr -> attr.getFiltro().getId())
                .collect(Collectors.toSet());

        // Crear atributos para filtros faltantes, manteniendo el orden por grupo
        for (Grupo grupo : gruposActuales) {
            if (grupo.getFiltros() != null) {
                // Ordenar filtros del grupo antes de crear atributos faltantes
                List<GrupoFiltroRelation> filtrosOrdenados = grupo.getFiltros().stream()
                        .sorted(Comparator.comparing(
                            relation -> relation.getOrden() != null ? relation.getOrden() : Integer.MAX_VALUE))
                        .collect(Collectors.toList());

                for (GrupoFiltroRelation filtroRelation : filtrosOrdenados) {
                    if (filtroRelation.getFiltro() != null) {
                        UUID filtroId = filtroRelation.getFiltro().getId();

                        if (filtroId != null && !filtrosExistentes.contains(filtroId)) {
                            Atributo nuevoAtributo = new Atributo();
                            nuevoAtributo.setId(null);
                            nuevoAtributo.setFiltro(filtroRelation.getFiltro());
                            initializeAtributoDefaultValue(nuevoAtributo, filtroRelation.getFiltro());

                            atributosActualizados.add(nuevoAtributo);
                            log.debug("Agregado atributo faltante para filtro: {} (orden: {})",
                                    filtroRelation.getFiltro().getNombreFiltro(), filtroRelation.getOrden());
                        }
                    }
                }
            }
        }

        log.info("Atributos actualizados: {} existentes + {} nuevos = {} total",
                atributosExistentes.size(),
                atributosActualizados.size() - atributosExistentes.size(),
                atributosActualizados.size());

        return atributosActualizados;
    }

    /**
     * Verifica y corrige el orden de atributos existentes según los grupos.
     * Útil para productos cargados desde el servidor.
     */
    public List<Atributo> ensureCorrectOrder(Set<Atributo> atributos, Set<Grupo> grupos) {
        Map<String, List<Atributo>> groupedAtributos = groupExistingAtributos(atributos, grupos);

        List<Atributo> orderedAtributos = new ArrayList<>();

        // Agregar atributos en el orden correcto por grupo
        for (Grupo grupo : grupos) {
            String grupoNombre = getGrupoPrincipalName(grupo);
            List<Atributo> atributosDelGrupo = groupedAtributos.get(grupoNombre);

            if (atributosDelGrupo != null) {
                orderedAtributos.addAll(atributosDelGrupo);
            }
        }

        // Agregar extras al final
        List<Atributo> extras = groupedAtributos.get("Extras");
        if (extras != null) {
            orderedAtributos.addAll(extras);
        }

        log.debug("Orden corregido: {} atributos organizados", orderedAtributos.size());
        return orderedAtributos;
    }

    /**
     * Elimina los atributos asociados a un grupo específico
     */
    public Set<Atributo> removeAtributosForGrupo(Set<Atributo> atributosExistentes, Grupo grupoEliminado) {
        if (grupoEliminado == null || grupoEliminado.getFiltros() == null) {
            return new HashSet<>(atributosExistentes);
        }

        // Obtener IDs de filtros del grupo eliminado
        Set<UUID> filtrosDelGrupoEliminado = grupoEliminado.getFiltros().stream()
                .filter(relation -> relation.getFiltro() != null && relation.getFiltro().getId() != null)
                .map(relation -> relation.getFiltro().getId())
                .collect(Collectors.toSet());

        // Filtrar atributos que NO pertenecen al grupo eliminado
        Set<Atributo> atributosRestantes = atributosExistentes.stream()
                .filter(atributo -> {
                    if (atributo.getFiltro() == null || atributo.getFiltro().getId() == null) {
                        return true; // Mantener atributos sin filtro definido
                    }
                    return !filtrosDelGrupoEliminado.contains(atributo.getFiltro().getId());
                })
                .collect(Collectors.toSet());

        log.debug("Eliminados {} atributos del grupo: {}",
                atributosExistentes.size() - atributosRestantes.size(),
                getGrupoPrincipalName(grupoEliminado));

        return atributosRestantes;
    }

    /**
     * Inicializa un atributo con valores por defecto según el tipo de filtro
     */
    private void initializeAtributoDefaultValue(Atributo atributo, Filtro filtro) {
        if (filtro.getTipo() == null) return;
        
        switch (filtro.getTipo()) {
            case CADENA_TEXTO:
            case OPCION_MULTIPLE:
            case DICOTOMICO:
            case COMPUESTO:
                atributo.setDato("");
                break;
            case NUMERICO:
                atributo.setDato("0.0");
                break;
        }
    }
    
    /**
     * Crea un mapa optimizado de filtro ID -> información del grupo (nombre + orden)
     */
    private Map<UUID, GrupoInfo> createOptimizedFiltroToGrupoMap(Set<Grupo> grupos) {
        Map<UUID, GrupoInfo> filtroToGrupoInfoMap = new HashMap<>();

        for (Grupo grupo : grupos) {
            String grupoNombre = getGrupoPrincipalName(grupo);

            if (grupo.getFiltros() != null) {
                for (GrupoFiltroRelation filtroRelation : grupo.getFiltros()) {
                    if (filtroRelation.getFiltro() != null && filtroRelation.getFiltro().getId() != null) {
                        Integer orden = filtroRelation.getOrden() != null ? filtroRelation.getOrden() : Integer.MAX_VALUE;
                        GrupoInfo grupoInfo = new GrupoInfo(grupoNombre, orden);
                        filtroToGrupoInfoMap.put(filtroRelation.getFiltro().getId(), grupoInfo);
                    }
                }
            }
        }

        log.debug("Creado mapa optimizado con {} filtros", filtroToGrupoInfoMap.size());
        return filtroToGrupoInfoMap;
    }

    /**
     * Crea un mapa de filtro ID -> nombre de grupo para búsqueda rápida (método legacy)
     */
    private Map<UUID, String> createFiltroToGrupoMap(Set<Grupo> grupos) {
        Map<UUID, String> filtroToGrupoMap = new HashMap<>();

        for (Grupo grupo : grupos) {
            String grupoNombre = getGrupoPrincipalName(grupo);

            if (grupo.getFiltros() != null) {
                for (GrupoFiltroRelation filtroRelation : grupo.getFiltros()) {
                    if (filtroRelation.getFiltro() != null && filtroRelation.getFiltro().getId() != null) {
                        filtroToGrupoMap.put(filtroRelation.getFiltro().getId(), grupoNombre);
                    }
                }
            }
        }

        return filtroToGrupoMap;
    }
    
    /**
     * Obtiene el nombre principal de un grupo
     */
    private String getGrupoPrincipalName(Grupo grupo) {
        if (grupo.getNombresGrupo() != null && !grupo.getNombresGrupo().isEmpty()) {
            return grupo.getNombresGrupo().stream()
                    .filter(nombre -> Boolean.TRUE.equals(nombre.getIsPrincipal()))
                    .findFirst()
                    .map(NombreGrupo::getNombre)
                    .orElse(grupo.getNombresGrupo().iterator().next().getNombre());
        }
        return "Sin nombre";
    }
    
    /**
     * Obtiene el orden de un filtro dentro de un grupo
     */
    private Integer getFiltroOrden(Grupo grupo, Filtro filtro) {
        if (grupo.getFiltros() == null || filtro == null || filtro.getId() == null) {
            return Integer.MAX_VALUE;
        }
        
        return grupo.getFiltros().stream()
                .filter(relation -> filtro.getId().equals(relation.getFiltro().getId()))
                .findFirst()
                .map(GrupoFiltroRelation::getOrden)
                .orElse(Integer.MAX_VALUE);
    }
}
