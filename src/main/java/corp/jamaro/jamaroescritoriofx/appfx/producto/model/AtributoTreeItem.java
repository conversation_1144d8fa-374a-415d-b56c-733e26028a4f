package corp.jamaro.jamaroescritoriofx.appfx.producto.model;

import corp.jamaro.jamaroescritoriofx.appfx.producto.model.enums.TipoFiltro;
import lombok.Data;

/**
 * Modelo para representar items en el TreeTableView de atributos.
 * Puede ser un nodo grupo (padre) o un nodo atributo (hijo).
 */
@Data
public class AtributoTreeItem {
    
    public enum ItemType {
        GRUPO,
        ATRIBUTO
    }
    
    private ItemType type;
    
    // Para nodos GRUPO
    private Grupo grupo;
    private String grupoNombre;
    
    // Para nodos ATRIBUTO
    private Atributo atributo;
    private String filtroNombre;
    private String valor;
    private TipoFiltro tipoFiltro;
    private boolean isEditable;
    
    // Constructor para nodo GRUPO
    public AtributoTreeItem(Grupo grupo, String grupoNombre) {
        this.type = ItemType.GRUPO;
        this.grupo = grupo;
        this.grupoNombre = grupoNombre;
        this.isEditable = false;
    }
    
    // Constructor para nodo ATRIBUTO
    public AtributoTreeItem(Atributo atributo) {
        this.type = ItemType.ATRIBUTO;
        this.atributo = atributo;
        this.filtroNombre = atributo.getFiltro() != null ? atributo.getFiltro().getNombreFiltro() : "";
        this.tipoFiltro = atributo.getFiltro() != null ? atributo.getFiltro().getTipo() : null;
        this.valor = getAtributoValueAsString(atributo);
        this.isEditable = true;
    }
    
    // Constructor para nodo ATRIBUTO con grupo "Extras"
    public AtributoTreeItem(Atributo atributo, boolean isExtra) {
        this(atributo);
        // Marcar como extra si es necesario para estilos diferentes
    }
    
    /**
     * Obtiene el valor del atributo como string según su tipo
     */
    private String getAtributoValueAsString(Atributo atributo) {
        String dato = atributo.getDato();
        return dato != null ? dato : "";
    }
    
    /**
     * Actualiza el valor del atributo según su tipo
     */
    public void updateAtributoValue(String newValue) {
        if (atributo == null || tipoFiltro == null) return;
        
        // Validar y asignar nuevo valor según el tipo
        switch (tipoFiltro) {
            case CADENA_TEXTO:
            case OPCION_MULTIPLE:
            case DICOTOMICO:
            case COMPUESTO:
                // Para tipos de texto, asignar directamente (null si está vacío)
                atributo.setDato(newValue != null && !newValue.trim().isEmpty() ? newValue : null);
                break;
            case NUMERICO:
                // Para tipo numérico, validar que sea un número válido
                if (newValue != null && !newValue.trim().isEmpty()) {
                    try {
                        Double.parseDouble(newValue.trim());
                        atributo.setDato(newValue.trim());
                    } catch (NumberFormatException e) {
                        atributo.setDato(null);
                    }
                } else {
                    atributo.setDato(null);
                }
                break;
        }
        
        // Actualizar valor mostrado
        this.valor = getAtributoValueAsString(atributo);
    }
    
    /**
     * Verifica si este item es un nodo grupo
     */
    public boolean isGrupo() {
        return type == ItemType.GRUPO;
    }
    
    /**
     * Verifica si este item es un nodo atributo
     */
    public boolean isAtributo() {
        return type == ItemType.ATRIBUTO;
    }
    
    /**
     * Obtiene el texto a mostrar en la columna principal
     */
    public String getDisplayText() {
        if (isGrupo()) {
            return grupoNombre;
        } else {
            return filtroNombre;
        }
    }
    
    /**
     * Obtiene el valor a mostrar en la columna de valor
     */
    public String getDisplayValue() {
        if (isGrupo()) {
            return ""; // Los grupos no tienen valor
        } else {
            return valor != null ? valor : "";
        }
    }
}
