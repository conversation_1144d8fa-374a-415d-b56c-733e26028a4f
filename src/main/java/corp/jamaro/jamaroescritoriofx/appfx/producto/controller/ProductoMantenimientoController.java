package corp.jamaro.jamaroescritoriofx.appfx.producto.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.*;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.ProductoMantenimientoService;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.GrupoCategoriaService;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.AtributoGroupingService;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.Vehiculo;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.model.VehiculoNombre;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.service.VehiculoService;
import corp.jamaro.jamaroescritoriofx.appfx.vehiculo.controller.VehiculoMantenimientoController;
import corp.jamaro.jamaroescritoriofx.appfx.model.file.ToBucketFileRelation;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.SearchProductGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.searchproduct.SearchProductGuiController;
import javafx.application.Platform;
import javafx.beans.property.ReadOnlyObjectWrapper;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.layout.HBox;
import javafx.scene.layout.FlowPane;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.CustomTextField;
import org.controlsfx.control.textfield.TextFields;
import org.kordamp.ikonli.javafx.FontIcon;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.core.scheduler.Schedulers;

import java.net.URL;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@Scope("prototype")
@RequiredArgsConstructor
public class ProductoMantenimientoController extends BaseController {

    private final ProductoMantenimientoService productoMantenimientoService;
    private final GrupoCategoriaService grupoCategoriaService;
    private final VehiculoService vehiculoService;
    private final SearchProductGuiService searchProductGuiService;
    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil;
    private final AtributoGroupingService atributoGroupingService;
    private final AtributoTreeManager atributoTreeManager;
    private final AtributoEditingController atributoEditingController;

    // FXML Components
    @FXML private ProgressIndicator progressIndicator;
    @FXML private CustomTextField txtBuscarCodProductoOld;
    @FXML private Button btnBuscarProducto;
    @FXML private TextField txtCodProductoOld;
    @FXML private CustomTextField txtDescripcion;
    @FXML private Button btnNuevo;
    @FXML private Button btnGuardar;
    @FXML private Button btnCancelar;

    // Atributos section
    @FXML private TreeTableView<AtributoTreeItem> treeAtributos;
    @FXML private TreeTableColumn<AtributoTreeItem, String> colAtributoNombre;
    @FXML private TreeTableColumn<AtributoTreeItem, String> colAtributoValor;
    @FXML private TreeTableColumn<AtributoTreeItem, Void> colAtributoAcciones;

    // Vehículos section
    @FXML private CustomTextField txtBuscarVehiculo;
    @FXML private TableView<Vehiculo> tblVehiculos;
    @FXML private TableColumn<Vehiculo, String> colVehiculoNombre;
    @FXML private TableColumn<Vehiculo, String> colVehiculoMarca;
    @FXML private TableColumn<Vehiculo, String> colVehiculoModelo;
    @FXML private TableColumn<Vehiculo, Void> colVehiculoAcciones;

    // Grupos section
    @FXML private CustomTextField txtBuscarGrupo;
    @FXML private FlowPane flowPaneGrupos;

    // Códigos de Fábrica section
    @FXML private CustomTextField txtBuscarCodigoFabrica;
    @FXML private FlowPane flowPaneCodigosFabrica;

    // Files section
    @FXML private Button btnAgregarArchivo;
    @FXML private FlowPane flowPaneArchivos;

    // State variables
    private Producto currentProducto;
    private boolean isCreatingNew = false;
    private AutoCompletionBinding<NombreGrupo> grupoAutoCompletionBinding;
    private AutoCompletionBinding<VehiculoNombre> vehiculoAutoCompletionBinding;
    private org.controlsfx.control.textfield.AutoCompletionBinding<CodigoFabrica> codigoFabricaAutoCompletionBinding;
    private ObservableList<Atributo> atributosData = FXCollections.observableArrayList();
    private ObservableList<Vehiculo> vehiculosData = FXCollections.observableArrayList();
    private ObservableList<Grupo> gruposData = FXCollections.observableArrayList();
    private ObservableList<CodigoFabrica> codigosFabricaData = FXCollections.observableArrayList();
    private ObservableList<ToBucketFileRelation> filesData = FXCollections.observableArrayList();

    // SearchProductGui state variables
    private SearchProductGuiController searchProductGuiController;
    private javafx.stage.Stage searchProductGuiStage;
    private java.util.UUID searchProductGuiId;
    private javafx.scene.Parent searchProductGuiView;

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        setupTables();
        setupEventHandlers();
        setupInitialState();
    }

    private void setupTables() {
        setupAtributosTreeTable();
        setupVehiculosTable();
        // Grupos and Códigos de Fábrica now use FlowPanes instead of tables
    }

    private void setupAtributosTreeTable() {
        // Inicializar el AtributoTreeManager
        atributoTreeManager.initialize(
            treeAtributos,
            colAtributoNombre,
            colAtributoValor,
            colAtributoAcciones,
            atributosData
        );

        // Configurar callback de edición
        atributoTreeManager.setOnEditAtributo(atributo ->
            atributoEditingController.showEditAtributoDialog(atributo, this::onAtributoEdited));
    }

    private void setupVehiculosTable() {
        // Configurar constraints responsivos para las columnas
        setupVehiculosTableConstraints();

        colVehiculoNombre.setCellValueFactory(cellData -> {
            Vehiculo vehiculo = cellData.getValue();
            String nombre = vehiculo.getNombres() != null && !vehiculo.getNombres().isEmpty()
                ? vehiculo.getNombres().iterator().next().getNombre() : "";
            return new ReadOnlyObjectWrapper<>(nombre);
        });
        colVehiculoNombre.setResizable(true);

        colVehiculoMarca.setCellValueFactory(cellData -> {
            Vehiculo vehiculo = cellData.getValue();
            String marca = vehiculo.getVehiculoMarca() != null ? vehiculo.getVehiculoMarca().getMarca() : "";
            return new ReadOnlyObjectWrapper<>(marca);
        });
        colVehiculoMarca.setResizable(true);

        colVehiculoModelo.setCellValueFactory(cellData -> {
            Vehiculo vehiculo = cellData.getValue();
            String modelo = vehiculo.getVehiculoModelo() != null ? vehiculo.getVehiculoModelo().getModelo() : "";
            return new ReadOnlyObjectWrapper<>(modelo);
        });
        colVehiculoModelo.setResizable(true);

        colVehiculoAcciones.setCellFactory(column -> new TableCell<Vehiculo, Void>() {
            private final Button deleteButton = new Button();

            {
                deleteButton.setGraphic(new FontIcon("fas-trash"));
                deleteButton.getStyleClass().addAll("button", "danger-button");
                deleteButton.setOnAction(e -> {
                    Vehiculo item = getTableView().getItems().get(getIndex());
                    deleteVehiculo(item);
                });
                deleteButton.setTooltip(new Tooltip("Eliminar vehículo"));
            }

            @Override
            protected void updateItem(Void item, boolean empty) {
                super.updateItem(item, empty);
                if (empty || getIndex() >= getTableView().getItems().size()) {
                    setGraphic(null);
                } else {
                    HBox buttons = new HBox(5, deleteButton);
                    buttons.setAlignment(Pos.CENTER);
                    setGraphic(buttons);
                }
            }
        });
        colVehiculoAcciones.setResizable(true);

        // Configurar la tabla
        tblVehiculos.setItems(vehiculosData);
        tblVehiculos.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY);

        // Agregar listener para cambios de tamaño
        tblVehiculos.widthProperty().addListener((obs, oldWidth, newWidth) -> {
            runOnUiThread(() -> tblVehiculos.requestLayout());
        });
    }

    /**
     * Configura constraints responsivos para las columnas de vehículos
     */
    private void setupVehiculosTableConstraints() {
        // Configurar anchos mínimos y máximos para responsividad
        colVehiculoNombre.setMinWidth(200);
        colVehiculoNombre.setPrefWidth(300);
        colVehiculoNombre.setMaxWidth(Double.MAX_VALUE);

        colVehiculoMarca.setMinWidth(120);
        colVehiculoMarca.setPrefWidth(150);
        colVehiculoMarca.setMaxWidth(Double.MAX_VALUE);

        colVehiculoModelo.setMinWidth(120);
        colVehiculoModelo.setPrefWidth(150);
        colVehiculoModelo.setMaxWidth(Double.MAX_VALUE);

        colVehiculoAcciones.setMinWidth(80);
        colVehiculoAcciones.setPrefWidth(100);
        colVehiculoAcciones.setMaxWidth(120);
    }



    private void setupEventHandlers() {
        setupCodProductoOldSearch();
        setupButtonHandlers();
        setupIdFieldHandlers();
        setupGrupoSearch();
        setupVehiculoSearch();
        setupCodigoFabricaSearch();
    }

    private void setupCodigoFabricaSearch() {
        // Setup autocompletion for factory code search con manejo robusto de interrupciones
        codigoFabricaAutoCompletionBinding = TextFields.bindAutoCompletion(txtBuscarCodigoFabrica, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty()) {
                return Collections.emptyList();
            }

            runOnUiThread(() -> progressIndicator.setVisible(true));

            try {
                // Usar blockOptional para manejar interrupciones de forma más elegante
                Optional<List<CodigoFabrica>> suggestions = productoMantenimientoService.buscarCodigoFabricaPorCodigo(userText)
                        .collectList()
                        .onErrorReturn(Collections.emptyList())
                        .blockOptional(java.time.Duration.ofSeconds(2));

                runOnUiThread(() -> progressIndicator.setVisible(false));

                return suggestions.orElse(Collections.emptyList());

            } catch (RuntimeException e) {
                runOnUiThread(() -> progressIndicator.setVisible(false));

                // Verificar si la causa es una InterruptedException
                if (e.getCause() instanceof InterruptedException) {
                    Thread.currentThread().interrupt(); // Restaurar el estado de interrupción
                    log.debug("Búsqueda de códigos de fábrica interrumpida (normal en autocompletado rápido)");
                } else {
                    log.error("Error searching for factory codes: {}", e.getMessage(), e);
                }
                return Collections.emptyList();
            } catch (Exception e) {
                runOnUiThread(() -> progressIndicator.setVisible(false));
                log.error("Error searching for factory codes: {}", e.getMessage(), e);
                return Collections.emptyList();
            }
        });

        // Handle selection from autocomplete
        codigoFabricaAutoCompletionBinding.setOnAutoCompleted(event -> {
            CodigoFabrica selectedCodigo = event.getCompletion();
            if (selectedCodigo != null) {
                addCodigoFabricaToProduct(selectedCodigo);
                txtBuscarCodigoFabrica.clear();
            }
        });

        // Handle ENTER key press for creating new factory codes when no matches found
        txtBuscarCodigoFabrica.setOnAction(event -> {
            String searchText = txtBuscarCodigoFabrica.getText().trim();
            if (!searchText.isEmpty()) {
                // Check if there are any suggestions usando enfoque reactivo
                try {
                    Optional<List<CodigoFabrica>> suggestions = productoMantenimientoService.buscarCodigoFabricaPorCodigo(searchText)
                            .collectList()
                            .onErrorReturn(Collections.emptyList())
                            .blockOptional(java.time.Duration.ofSeconds(2));

                    if (suggestions.isEmpty() || suggestions.get().isEmpty()) {
                        // No matches found, ask user if they want to create a new factory code
                        askToCreateNewCodigoFabrica(searchText);
                    }
                } catch (Exception e) {
                    log.debug("Error verificando códigos de fábrica existentes para creación: {}", e.getMessage());
                    // En caso de error, permitir la creación
                    askToCreateNewCodigoFabrica(searchText);
                }
            }
        });
    }


    private String lastSearchedCode = ""; // Para evitar búsquedas duplicadas

    private void setupCodProductoOldSearch() {
        // Solo usar onAction para evitar búsquedas duplicadas
        txtBuscarCodProductoOld.setOnAction(event -> {
            String codProductoOld = txtBuscarCodProductoOld.getText();
            if (codProductoOld != null && !codProductoOld.trim().isEmpty()) {
                String trimmedCode = codProductoOld.trim();
                // Evitar búsquedas duplicadas
                if (!trimmedCode.equals(lastSearchedCode)) {
                    lastSearchedCode = trimmedCode;
                    searchByCodProductoOld(trimmedCode);
                }
            }
        });
    }

    private void searchByCodProductoOld(String codProductoOld) {
        if (isCreatingNew) {
            return; // Don't search when creating new
        }

        log.info("Iniciando búsqueda de producto con código: {}", codProductoOld);
        progressIndicator.setVisible(true);

        // Usar BaseController para manejo reactivo más limpio
        subscribeMonoWithUiUpdate(
            productoMantenimientoService.buscarProductoPorCodProductoOld(codProductoOld),
            producto -> {
                if (producto != null) {
                    // Producto encontrado
                    log.info("Producto encontrado: {}", producto.getCodProductoOld());
                    loadProductoData(producto, false);
                    txtBuscarCodProductoOld.clear();
                    lastSearchedCode = "";
                } else {
                    // Producto no encontrado (Mono.empty())
                    log.info("Producto no encontrado - mostrando mensaje");
                    txtBuscarCodProductoOld.clear();
                    lastSearchedCode = "";
                    alertUtil.showError("No se encontró producto con código: " + codProductoOld);
                }
                progressIndicator.setVisible(false);
            },
            error -> {
                txtBuscarCodProductoOld.clear();
                lastSearchedCode = "";
                progressIndicator.setVisible(false);
                alertUtil.showError("Error al buscar producto: " + error.getMessage());
            },
            Duration.ofSeconds(10)
        );
    }

    private void setupButtonHandlers() {
        btnNuevo.setOnAction(e -> startCreatingNew());
        btnGuardar.setOnAction(e -> saveProducto());
        btnCancelar.setOnAction(e -> cancelOperation());
        btnBuscarProducto.setOnAction(e -> openSearchProductGui());

        btnAgregarArchivo.setOnAction(e -> showAddArchivoDialog());
    }

    private void setupIdFieldHandlers() {
        // txtCodProductoOld now serves as the identifier field
        // No automatic loading by ID since we removed txtId field
    }

    private void setupGrupoSearch() {
        // Setup autocompletion for group search
        grupoAutoCompletionBinding = TextFields.bindAutoCompletion(txtBuscarGrupo, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty()) {
                return Collections.emptyList();
            }

            runOnUiThread(() -> progressIndicator.setVisible(true));

            try {
                // Usar blockOptional para manejar interrupciones de forma más elegante
                Optional<List<NombreGrupo>> suggestions = grupoCategoriaService.buscarNombresGrupoPorRegex(userText)
                        .collectList()
                        .onErrorReturn(Collections.emptyList())
                        .blockOptional(java.time.Duration.ofSeconds(2));

                runOnUiThread(() -> progressIndicator.setVisible(false));

                return suggestions.orElse(Collections.emptyList());

            } catch (RuntimeException e) {
                runOnUiThread(() -> progressIndicator.setVisible(false));

                // Verificar si la causa es una InterruptedException
                if (e.getCause() instanceof InterruptedException) {
                    Thread.currentThread().interrupt(); // Restaurar el estado de interrupción
                    log.debug("Búsqueda de grupos interrumpida (normal en autocompletado rápido)");
                } else {
                    log.error("Error searching for groups: {}", e.getMessage(), e);
                }
                return Collections.emptyList();
            } catch (Exception e) {
                runOnUiThread(() -> progressIndicator.setVisible(false));
                log.error("Error searching for groups: {}", e.getMessage(), e);
                return Collections.emptyList();
            }
        });

        // Handle selection from autocomplete
        grupoAutoCompletionBinding.setOnAutoCompleted(event -> {
            NombreGrupo selectedNombre = event.getCompletion();
            if (selectedNombre != null && selectedNombre.getId() != null) {
                loadGrupoByNombreId(selectedNombre.getId());
                txtBuscarGrupo.clear();
            }
        });

        // Handle ENTER key press for creating new groups when no matches found
        txtBuscarGrupo.setOnAction(event -> {
            String searchText = txtBuscarGrupo.getText().trim();
            if (!searchText.isEmpty()) {
                // Check if there are any suggestions usando enfoque reactivo
                try {
                    Optional<List<NombreGrupo>> suggestions = grupoCategoriaService.buscarNombresGrupoPorRegex(searchText)
                            .collectList()
                            .onErrorReturn(Collections.emptyList())
                            .blockOptional(java.time.Duration.ofSeconds(2));

                    if (suggestions.isEmpty() || suggestions.get().isEmpty()) {
                        // No matches found, ask user if they want to create a new group
                        askToCreateNewGroup(searchText);
                    }
                } catch (Exception e) {
                    log.debug("Error verificando grupos existentes para creación: {}", e.getMessage());
                    // En caso de error, permitir la creación
                    askToCreateNewGroup(searchText);
                }
            }
        });
    }

    private void setupVehiculoSearch() {
        // Setup autocompletion for vehicle search
        vehiculoAutoCompletionBinding = TextFields.bindAutoCompletion(txtBuscarVehiculo, request -> {
            String userText = Optional.ofNullable(request.getUserText()).orElse("").trim();
            if (userText.isEmpty()) {
                return Collections.emptyList();
            }

            runOnUiThread(() -> progressIndicator.setVisible(true));

            try {
                // Usar blockOptional para manejar interrupciones de forma más elegante
                Optional<List<VehiculoNombre>> suggestions = vehiculoService.buscarNombresVehiculoPorRegex(userText)
                        .collectList()
                        .onErrorReturn(Collections.emptyList())
                        .blockOptional(java.time.Duration.ofSeconds(2));

                runOnUiThread(() -> progressIndicator.setVisible(false));

                return suggestions.orElse(Collections.emptyList());

            } catch (RuntimeException e) {
                runOnUiThread(() -> progressIndicator.setVisible(false));

                // Verificar si la causa es una InterruptedException
                if (e.getCause() instanceof InterruptedException) {
                    Thread.currentThread().interrupt(); // Restaurar el estado de interrupción
                    log.debug("Búsqueda de vehículos interrumpida (normal en autocompletado rápido)");
                } else {
                    log.error("Error searching for vehicles: {}", e.getMessage(), e);
                }
                return Collections.emptyList();
            } catch (Exception e) {
                runOnUiThread(() -> progressIndicator.setVisible(false));
                log.error("Error searching for vehicles: {}", e.getMessage(), e);
                return Collections.emptyList();
            }
        });

        // Handle selection from autocomplete
        vehiculoAutoCompletionBinding.setOnAutoCompleted(event -> {
            VehiculoNombre selectedNombre = event.getCompletion();
            if (selectedNombre != null && selectedNombre.getId() != null) {
                loadVehiculoByNombreId(selectedNombre.getId());
                txtBuscarVehiculo.clear();
            }
        });

        // Handle ENTER key press for creating new vehicles when no matches found
        txtBuscarVehiculo.setOnAction(event -> {
            String searchText = txtBuscarVehiculo.getText().trim();
            if (!searchText.isEmpty()) {
                // Check if there are any suggestions usando enfoque reactivo
                try {
                    Optional<List<VehiculoNombre>> suggestions = vehiculoService.buscarNombresVehiculoPorRegex(searchText)
                            .collectList()
                            .onErrorReturn(Collections.emptyList())
                            .blockOptional(java.time.Duration.ofSeconds(2));

                    if (suggestions.isEmpty() || suggestions.get().isEmpty()) {
                        // No matches found, ask user if they want to create a new vehicle
                        askToCreateNewVehiculo(searchText);
                    }
                } catch (Exception e) {
                    log.debug("Error verificando vehículos existentes para creación: {}", e.getMessage());
                    // En caso de error, permitir la creación
                    askToCreateNewVehiculo(searchText);
                }
            }
        });
    }

    private void loadVehiculoByNombreId(UUID nombreVehiculoId) {
        progressIndicator.setVisible(true);
        subscribeMonoWithUiUpdate(
            vehiculoService.obtenerVehiculoPorNombreVehiculoId(nombreVehiculoId),
            this::addVehiculoToProduct,
            logAndShowError("cargando vehículo", this::showErrorMessage)
        );
    }

    private void loadGrupoByNombreId(UUID nombreGrupoId) {
        progressIndicator.setVisible(true);
        subscribeMonoWithUiUpdate(
            grupoCategoriaService.obtenerGrupoPorNombreGrupoId(nombreGrupoId),
            this::addGrupoToProduct,
            logAndShowError("cargando grupo", this::showErrorMessage)
        );
    }

    private void addVehiculoToProduct(Vehiculo vehiculo) {
        progressIndicator.setVisible(false);
        if (vehiculo != null) {
            // Check if vehicle is already added
            boolean alreadyExists = vehiculosData.stream()
                .anyMatch(existingVehiculo -> existingVehiculo.getId().equals(vehiculo.getId()));

            if (!alreadyExists) {
                vehiculosData.add(vehiculo);
                log.info("Vehículo agregado: {}", vehiculo.getId());
            } else {
                showErrorMessage("El vehículo ya está agregado al producto");
            }
        }
    }

    private void addGrupoToProduct(Grupo grupo) {
        progressIndicator.setVisible(false);
        if (grupo != null) {
            // Check if group is already added
            boolean alreadyExists = gruposData.stream()
                .anyMatch(existingGrupo -> existingGrupo.getId().equals(grupo.getId()));

            if (!alreadyExists) {
                gruposData.add(grupo);
                createGrupoComponent(grupo);

                // Si es un producto nuevo, crear atributos automáticamente
                if (isCreatingNew) {
                    atributoTreeManager.addAtributosForGrupo(grupo);
                } else {
                    // Para productos existentes, actualizar atributos
                    Set<Atributo> atributosActualizados = atributoGroupingService.updateAtributosForGroups(
                        new HashSet<>(atributosData), new HashSet<>(gruposData));
                    atributosData.clear();
                    atributosData.addAll(atributosActualizados);
                }

                updateAtributosTreeView();
                log.info("Grupo agregado: {}", grupo.getId());
            } else {
                showErrorMessage("El grupo ya está agregado al producto");
            }
        }
    }

    private void addCodigoFabricaToProduct(CodigoFabrica codigoFabrica) {
        if (codigoFabrica != null) {
            // Check if factory code is already added
            boolean alreadyExists = codigosFabricaData.stream()
                .anyMatch(existingCodigo -> {
                    if (existingCodigo.getId() != null && codigoFabrica.getId() != null) {
                        return existingCodigo.getId().equals(codigoFabrica.getId());
                    } else {
                        // Compare by codigo if IDs are null
                        return Objects.equals(existingCodigo.getCodigo(), codigoFabrica.getCodigo());
                    }
                });

            if (!alreadyExists) {
                codigosFabricaData.add(codigoFabrica);
                createCodigoFabricaComponent(codigoFabrica);
                log.info("Código de fábrica agregado: {}", codigoFabrica.getCodigo());
            } else {
                showErrorMessage("El código de fábrica ya está agregado al producto");
            }
        }
    }

    private void askToCreateNewGroup(String groupName) {
        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Crear Nuevo Grupo",
            "Grupo no encontrado",
            String.format("No se encontró ningún grupo con el nombre '%s'.\n¿Desea crear un nuevo grupo con este nombre?", groupName),
            "Crear",
            "Cancelar"
        );

        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            createNewGroupWithName(groupName);
        }

        txtBuscarGrupo.clear();
    }

    private void askToCreateNewVehiculo(String vehiculoName) {
        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Crear Nuevo Vehículo",
            "Vehículo no encontrado",
            String.format("No se encontró ningún vehículo con el nombre '%s'.\n¿Desea crear un nuevo vehículo con este nombre?", vehiculoName),
            "Crear",
            "Cancelar"
        );

        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            createNewVehiculoWithName(vehiculoName);
        }

        txtBuscarVehiculo.clear();
    }

    private void askToCreateNewCodigoFabrica(String codigoText) {
        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Crear Nuevo Código de Fábrica",
            "Código no encontrado",
            String.format("No se encontró ningún código de fábrica '%s'.\n¿Desea crear un nuevo código con este valor?", codigoText),
            "Crear",
            "Cancelar"
        );

        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            createNewCodigoFabricaWithCode(codigoText);
        }

        txtBuscarCodigoFabrica.clear();
    }

    private void createNewVehiculoWithName(String vehiculoName) {
        try {
            // Load VehiculoMantenimientoController
            javafx.scene.Parent vehiculoMantenimientoView = springFXMLLoader.load(FXMLEnum.CREAR_VEHICULO);
            VehiculoMantenimientoController vehiculoMantenimientoController = springFXMLLoader.getController(vehiculoMantenimientoView);

            // Create stage for vehicle creation
            javafx.stage.Stage vehicleCreationStage = new javafx.stage.Stage();
            vehicleCreationStage.setTitle("Crear Nuevo Vehículo");
            vehicleCreationStage.initModality(javafx.stage.Modality.WINDOW_MODAL);
            vehicleCreationStage.initOwner(txtBuscarVehiculo.getScene().getWindow());
            vehicleCreationStage.setScene(new javafx.scene.Scene(vehiculoMantenimientoView, 1000, 700));

            // Set up callback for when vehicle is created
            vehiculoMantenimientoController.createVehiculoWithName(vehiculoName, createdVehiculo -> {
                runOnUiThread(() -> {
                    addVehiculoToProduct(createdVehiculo);
                    vehicleCreationStage.close();
                });
            });

            vehicleCreationStage.show();

        } catch (Exception e) {
            log.error("Error al abrir VehiculoMantenimientoController: {}", e.getMessage(), e);
            showErrorMessage("Error al abrir el formulario de creación de vehículo: " + e.getMessage());
        }
    }

    private void createNewGroupWithName(String groupName) {
        try {
            // Load GrupoCategoriaController
            javafx.scene.Parent grupoCategoriaView = springFXMLLoader.load(FXMLEnum.GRUPO_MANTENIMIENTO);
            GrupoCategoriaController grupoCategoriaController = springFXMLLoader.getController(grupoCategoriaView);

            // Create stage for group creation
            javafx.stage.Stage groupCreationStage = new javafx.stage.Stage();
            groupCreationStage.setTitle("Crear Nuevo Grupo");
            groupCreationStage.initModality(javafx.stage.Modality.WINDOW_MODAL);
            groupCreationStage.initOwner(txtBuscarGrupo.getScene().getWindow());
            groupCreationStage.setScene(new javafx.scene.Scene(grupoCategoriaView, 800, 600));

            // Set up callback for when group is created
            grupoCategoriaController.createGroupWithName(groupName, createdGroup -> {
                runOnUiThread(() -> {
                    addGrupoToProduct(createdGroup);
                    groupCreationStage.close();
                });
            });

            groupCreationStage.show();

        } catch (Exception e) {
            log.error("Error al abrir GrupoCategoriaController: {}", e.getMessage(), e);
            showErrorMessage("Error al abrir el formulario de creación de grupo: " + e.getMessage());
        }
    }

    private void createNewCodigoFabricaWithCode(String codigoText) {
        // Create new CodigoFabrica with null ID (server will assign it later)
        CodigoFabrica newCodigoFabrica = new CodigoFabrica();
        newCodigoFabrica.setId(null); // Server will assign ID when saving the complete Product
        newCodigoFabrica.setCodigo(codigoText.trim());
        
        // Add to product
        addCodigoFabricaToProduct(newCodigoFabrica);
        
        log.info("Nuevo código de fábrica creado localmente: {}", codigoText);
    }

    private void setupInitialState() {
        enableSearchMode();
        clearAll();
    }

    private void loadProductoById(UUID productoId) {
        progressIndicator.setVisible(true);
        subscribeMonoWithUiUpdate(
            productoMantenimientoService.obtenerProductoPorId(productoId),
            this::loadProductoData,
            logAndShowError("cargando producto por ID", this::showErrorMessage)
        );
    }

    private void loadProductoData(Producto producto) {
        loadProductoData(producto, true);
    }

    private void loadProductoData(Producto producto, boolean hideProgressIndicator) {
        if (hideProgressIndicator) {
            progressIndicator.setVisible(false);
        }
        this.currentProducto = producto;

        // Load basic data
        txtCodProductoOld.setText(producto.getCodProductoOld() != null ? producto.getCodProductoOld() : "");
        txtDescripcion.setText(producto.getDescripcion() != null ? producto.getDescripcion() : "");

        // Load related data - IMPORTANTE: cargar grupos primero para que los atributos se ordenen correctamente
        loadGrupos(producto.getGrupos());
        loadAtributos(producto.getAtributos()); // Ahora se ordenarán según los grupos cargados
        loadVehiculos(producto.getVehiculos());
        loadCodigosFabrica(producto.getCodigosFabrica());
        loadFiles(producto.getFiles());

        enableEditMode();
    }

    private void loadAtributos(Set<Atributo> atributos) {
        atributosData.clear();
        if (atributos != null) {
            // Para productos existentes, asegurar el orden correcto según los grupos
            if (!gruposData.isEmpty()) {
                List<Atributo> atributosOrdenados = atributoGroupingService.ensureCorrectOrder(
                    atributos, new HashSet<>(gruposData));
                atributosData.addAll(atributosOrdenados);
                log.debug("Atributos cargados y ordenados: {}", atributosOrdenados.size());
            } else {
                // Si no hay grupos aún, cargar tal como vienen
                atributosData.addAll(atributos);
                log.debug("Atributos cargados sin ordenar: {}", atributos.size());
            }
        }
        updateAtributosTreeView();
    }

    private void loadVehiculos(Set<Vehiculo> vehiculos) {
        vehiculosData.clear();
        if (vehiculos != null) {
            vehiculosData.addAll(vehiculos);
        }
    }

    private void loadGrupos(Set<Grupo> grupos) {
        gruposData.clear();
        flowPaneGrupos.getChildren().clear();
        if (grupos != null) {
            gruposData.addAll(grupos);
            for (Grupo grupo : grupos) {
                createGrupoComponent(grupo);
            }
        }
    }

    private void loadCodigosFabrica(Set<CodigoFabrica> codigosFabrica) {
        codigosFabricaData.clear();
        flowPaneCodigosFabrica.getChildren().clear();
        if (codigosFabrica != null) {
            codigosFabricaData.addAll(codigosFabrica);
            for (CodigoFabrica codigo : codigosFabrica) {
                createCodigoFabricaComponent(codigo);
            }
        }
    }

    private void loadFiles(Set<ToBucketFileRelation> files) {
        filesData.clear();
        flowPaneArchivos.getChildren().clear();
        if (files != null) {
            filesData.addAll(files);
            // TODO: Create UI components for file display
        }
    }

    private void createGrupoComponent(Grupo grupo) {
        // Create a label showing the principal name of the group
        String nombrePrincipal = getGrupoPrincipalName(grupo);

        HBox grupoBox = new HBox(5);
        grupoBox.setAlignment(Pos.CENTER_LEFT);
        grupoBox.getStyleClass().add("tag-item");

        Label lblGrupo = new Label(nombrePrincipal);
        lblGrupo.getStyleClass().add("tag-text");

        Button btnRemove = new Button();
        btnRemove.setGraphic(new FontIcon("fas-times"));
        btnRemove.getStyleClass().addAll("button", "danger-button", "small-button");
        btnRemove.setOnAction(e -> deleteGrupoFromUI(grupo, grupoBox));

        grupoBox.getChildren().addAll(lblGrupo, btnRemove);
        flowPaneGrupos.getChildren().add(grupoBox);
    }

    private void createCodigoFabricaComponent(CodigoFabrica codigoFabrica) {
        HBox codigoBox = new HBox(5);
        codigoBox.setAlignment(Pos.CENTER_LEFT);
        codigoBox.getStyleClass().add("tag-item");

        Label lblCodigo = new Label(codigoFabrica.getCodigo());
        lblCodigo.getStyleClass().add("tag-text");

        Button btnRemove = new Button();
        btnRemove.setGraphic(new FontIcon("fas-times"));
        btnRemove.getStyleClass().addAll("button", "danger-button", "small-button");
        btnRemove.setOnAction(e -> deleteCodigoFabricaFromUI(codigoFabrica, codigoBox));

        codigoBox.getChildren().addAll(lblCodigo, btnRemove);
        flowPaneCodigosFabrica.getChildren().add(codigoBox);
    }

    private void startCreatingNew() {
        clearAll();
        isCreatingNew = true;
        currentProducto = new Producto();
        currentProducto.setId(UUID.randomUUID());
        enableCreateMode();
    }

    private void saveProducto() {
        if (currentProducto == null) {
            showErrorMessage("No hay producto para guardar");
            return;
        }

        // Update producto with form data
        currentProducto.setCodProductoOld(txtCodProductoOld.getText().trim());
        currentProducto.setDescripcion(txtDescripcion.getText().trim());

        // Validate required fields before saving
        if (!validateProductoBeforeSave()) {
            return;
        }

        // Filtrar atributos vacíos antes de guardar
        Set<Atributo> atributosNoVacios = filterNonEmptyAtributos(atributosData);
        currentProducto.setAtributos(atributosNoVacios);

        currentProducto.setVehiculos(new HashSet<>(vehiculosData));
        currentProducto.setGrupos(new HashSet<>(gruposData));
        currentProducto.setCodigosFabrica(new HashSet<>(codigosFabricaData));
        currentProducto.setFiles(new HashSet<>(filesData));

        progressIndicator.setVisible(true);

        if (isCreatingNew) {
            subscribeMonoWithUiUpdate(
                productoMantenimientoService.crearProducto(currentProducto),
                this::onSaveSuccess,
                logAndShowError("creando producto", this::showErrorMessage)
            );
        } else {
            subscribeMonoWithUiUpdate(
                productoMantenimientoService.actualizarProducto(currentProducto),
                this::onSaveSuccess,
                logAndShowError("actualizando producto", this::showErrorMessage)
            );
        }
    }

    private void onSaveSuccess(Producto savedProducto) {
        progressIndicator.setVisible(false);
        showSuccessMessage(isCreatingNew ? "Producto creado exitosamente" : "Producto actualizado exitosamente");
        loadProductoData(savedProducto);
        isCreatingNew = false;
    }

    private void cancelOperation() {
        clearAll();
        enableSearchMode();
    }


    // Dialog methods for adding related entities


    private void showAddArchivoDialog() {
        // TODO: Implement file picker dialog
        showErrorMessage("Funcionalidad de agregar archivo no implementada aún");
    }

    // Delete methods for related entities
    private void editAtributo(Atributo atributo) {
        atributoEditingController.showEditAtributoDialog(atributo, this::onAtributoEdited);
    }



    private void deleteVehiculo(Vehiculo vehiculo) {
        String nombre = vehiculo.getNombres() != null && !vehiculo.getNombres().isEmpty() 
            ? vehiculo.getNombres().iterator().next().getNombre() : "Sin nombre";
        String marca = vehiculo.getVehiculoMarca() != null ? vehiculo.getVehiculoMarca().getMarca() : "Sin marca";
        String modelo = vehiculo.getVehiculoModelo() != null ? vehiculo.getVehiculoModelo().getModelo() : "Sin modelo";
        String mensaje = String.format("¿Está seguro que desea eliminar el vehículo '%s - %s %s'?", nombre, marca, modelo);
        
        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Confirmar eliminación", 
            "Eliminar Vehículo", 
            mensaje,
            "Eliminar",
            "Cancelar"
        );
        
        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            vehiculosData.remove(vehiculo);
        }
    }



    private void deleteCodigoFabrica(CodigoFabrica codigoFabrica) {
        String codigo = codigoFabrica.getCodigo() != null ? codigoFabrica.getCodigo() : "Sin código";
        String mensaje = String.format("¿Está seguro que desea eliminar el código de fábrica '%s'?", codigo);

        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Confirmar eliminación",
            "Eliminar Código de Fábrica",
            mensaje,
            "Eliminar",
            "Cancelar"
        );

        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            codigosFabricaData.remove(codigoFabrica);
        }
    }

    // Helper method to get principal name from grupo
    private String getGrupoPrincipalName(Grupo grupo) {
        if (grupo.getNombresGrupo() != null && !grupo.getNombresGrupo().isEmpty()) {
            return grupo.getNombresGrupo().stream()
                .filter(nombre -> Boolean.TRUE.equals(nombre.getIsPrincipal()))
                .findFirst()
                .map(NombreGrupo::getNombre)
                .orElse(grupo.getNombresGrupo().iterator().next().getNombre());
        }
        return "Sin nombre";
    }

    // UI-specific delete methods for FlowPane components
    private void deleteGrupoFromUI(Grupo grupo, HBox grupoBox) {
        String nombrePrincipal = getGrupoPrincipalName(grupo);

        // Contar cuántos atributos se eliminarán
        int atributosAEliminar = countAtributosForGrupo(grupo);

        String mensaje = String.format(
            "¿Está seguro que desea eliminar el grupo '%s'?\n\n" +
            "⚠️ Esto también eliminará %d atributo(s) asociado(s) a este grupo.",
            nombrePrincipal, atributosAEliminar
        );

        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Confirmar eliminación",
            "Eliminar Grupo y Atributos",
            mensaje,
            "Eliminar",
            "Cancelar"
        );

        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            flowPaneGrupos.getChildren().remove(grupoBox);
            gruposData.remove(grupo);

            // Eliminar atributos asociados al grupo eliminado
            Set<Atributo> atributosRestantes = atributoGroupingService.removeAtributosForGrupo(
                new HashSet<>(atributosData), grupo);
            atributosData.clear();
            atributosData.addAll(atributosRestantes);

            // Actualizar el TreeView de atributos
            updateAtributosTreeView();

            log.info("Grupo eliminado: {} - Atributos asociados también eliminados",
                    getGrupoPrincipalName(grupo));
        }
    }

    private void deleteCodigoFabricaFromUI(CodigoFabrica codigoFabrica, HBox codigoBox) {
        String codigo = codigoFabrica.getCodigo() != null ? codigoFabrica.getCodigo() : "Sin código";
        String mensaje = String.format("¿Está seguro que desea eliminar el código de fábrica '%s'?", codigo);
        
        Optional<ButtonType> result = alertUtil.showConfirmation(
            "Confirmar eliminación", 
            "Eliminar Código de Fábrica", 
            mensaje,
            "Eliminar",
            "Cancelar"
        );
        
        if (result.isPresent() && result.get().getButtonData() == ButtonBar.ButtonData.OK_DONE) {
            flowPaneCodigosFabrica.getChildren().remove(codigoBox);
            codigosFabricaData.remove(codigoFabrica);
        }
    }

    // Utility methods
    private String getAtributoValueAsString(Atributo atributo) {
        String dato = atributo.getDato();
        return dato != null ? dato : "";
    }

    /**
     * Actualiza el TreeTableView de atributos con los datos actuales
     */
    private void updateAtributosTreeView() {
        atributoTreeManager.updateTreeContent(new HashSet<>(atributosData), new HashSet<>(gruposData));
    }

    /**
     * Cuenta cuántos atributos pertenecen a un grupo específico
     */
    private int countAtributosForGrupo(Grupo grupo) {
        if (grupo == null || grupo.getFiltros() == null) {
            return 0;
        }

        Set<UUID> filtrosDelGrupo = grupo.getFiltros().stream()
                .filter(relation -> relation.getFiltro() != null && relation.getFiltro().getId() != null)
                .map(relation -> relation.getFiltro().getId())
                .collect(Collectors.toSet());

        return (int) atributosData.stream()
                .filter(atributo -> atributo.getFiltro() != null &&
                                  atributo.getFiltro().getId() != null &&
                                  filtrosDelGrupo.contains(atributo.getFiltro().getId()))
                .count();
    }

    /**
     * Callback cuando se edita un atributo
     */
    private void onAtributoEdited(Atributo atributo) {
        // Actualizar en la lista observable
        int index = atributosData.indexOf(atributo);
        if (index >= 0) {
            atributosData.set(index, atributo);
        }
        updateAtributosTreeView();
        log.info("Atributo editado: {}", atributo.getFiltro().getNombreFiltro());
    }

    /**
     * Valida que el producto tenga los campos requeridos antes de guardar
     */
    private boolean validateProductoBeforeSave() {
        StringBuilder errorMessages = new StringBuilder();
        
        // Validar que tenga código de producto
        String codProductoOld = txtCodProductoOld.getText().trim();
        if (codProductoOld == null || codProductoOld.isEmpty()) {
            errorMessages.append("• El producto debe tener un código de producto\n");
        }
        
        // Validar que tenga descripción
        String descripcion = txtDescripcion.getText().trim();
        if (descripcion == null || descripcion.isEmpty()) {
            errorMessages.append("• El producto debe tener una descripción\n");
        }
        
        // Validar que tenga al menos un grupo
        if (gruposData == null || gruposData.isEmpty()) {
            errorMessages.append("• El producto debe tener al menos un grupo\n");
        }
        
        // Si hay errores, mostrarlos y retornar false
        if (errorMessages.length() > 0) {
            String fullMessage = "No se puede guardar el producto. Faltan los siguientes campos requeridos:\n\n" + errorMessages.toString();
            showErrorMessage(fullMessage);
            return false;
        }
        
        return true;
    }

    /**
     * Filtra los atributos que no están vacíos para enviar al servidor
     */
    private Set<Atributo> filterNonEmptyAtributos(ObservableList<Atributo> atributos) {
        return atributos.stream()
                .filter(this::isAtributoNotEmpty)
                .collect(Collectors.toSet());
    }

    /**
     * Verifica si un atributo no está vacío según su tipo
     */
    private boolean isAtributoNotEmpty(Atributo atributo) {
        if (atributo.getFiltro() == null || atributo.getFiltro().getTipo() == null) {
            return false;
        }

        String dato = atributo.getDato();
        
        switch (atributo.getFiltro().getTipo()) {
            case CADENA_TEXTO:
            case OPCION_MULTIPLE:
            case DICOTOMICO:
            case COMPUESTO:
                return dato != null && !dato.trim().isEmpty();

            case NUMERICO:
                if (dato == null || dato.trim().isEmpty()) {
                    return false;
                }
                try {
                    Double.parseDouble(dato.trim());
                    return true;
                } catch (NumberFormatException e) {
                    return false;
                }

            default:
                return false;
        }
    }



    private void clearAll() {
        currentProducto = null;
        isCreatingNew = false;

        txtCodProductoOld.clear();
        txtDescripcion.clear();
        txtBuscarCodProductoOld.clear();
        txtBuscarGrupo.clear();
        txtBuscarVehiculo.clear();
        txtBuscarCodigoFabrica.clear();
        lastSearchedCode = ""; // Reset search state

        atributosData.clear();
        vehiculosData.clear();
        gruposData.clear();
        codigosFabricaData.clear();
        filesData.clear();

        flowPaneGrupos.getChildren().clear();
        flowPaneCodigosFabrica.getChildren().clear();
        flowPaneArchivos.getChildren().clear();

        // Limpiar el TreeTableView de atributos
        updateAtributosTreeView();

        progressIndicator.setVisible(false);
    }

    @Override
    public void onClose() {
        // Clean up autocompletion bindings
        if (grupoAutoCompletionBinding != null) {
            grupoAutoCompletionBinding.dispose();
            grupoAutoCompletionBinding = null;
        }
        
        if (vehiculoAutoCompletionBinding != null) {
            vehiculoAutoCompletionBinding.dispose();
            vehiculoAutoCompletionBinding = null;
        }
        
        if (codigoFabricaAutoCompletionBinding != null) {
            codigoFabricaAutoCompletionBinding.dispose();
            codigoFabricaAutoCompletionBinding = null;
        }

        // Close SearchProductGui if open
        closeSearchProductGui();

        // Call parent cleanup
        super.onClose();
    }

    private void enableSearchMode() {
        txtBuscarCodProductoOld.setDisable(false);  // Enable search by code
        btnBuscarProducto.setDisable(false);  // Enable search button
        txtCodProductoOld.setDisable(true);  // Not editable in search mode
        txtDescripcion.setDisable(true);
        txtBuscarGrupo.setDisable(true);  // Disable group search in search mode
        txtBuscarVehiculo.setDisable(true);  // Disable vehicle search in search mode
        txtBuscarCodigoFabrica.setDisable(true);  // Disable factory code search in search mode

        btnNuevo.setDisable(false);
        btnGuardar.setDisable(true);
        btnCancelar.setDisable(true);

        setRelatedButtonsDisabled(true);
    }

    private void enableEditMode() {
        txtBuscarCodProductoOld.setDisable(true);  // Disable search in edit mode
        btnBuscarProducto.setDisable(true);  // Disable search button in edit mode
        txtCodProductoOld.setDisable(true);  // Not editable in edit mode (serves as ID)
        txtDescripcion.setDisable(false);
        txtBuscarGrupo.setDisable(false);  // Enable group search in edit mode
        txtBuscarVehiculo.setDisable(false);  // Enable vehicle search in edit mode
        txtBuscarCodigoFabrica.setDisable(false);  // Enable factory code search in edit mode

        btnNuevo.setDisable(true);
        btnGuardar.setDisable(false);
        btnCancelar.setDisable(false);

        setRelatedButtonsDisabled(false);
    }

    private void enableCreateMode() {
        txtBuscarCodProductoOld.setDisable(true);  // Disable search in create mode
        btnBuscarProducto.setDisable(true);  // Disable search button in create mode
        txtCodProductoOld.setDisable(false);  // Only editable when creating new
        txtDescripcion.setDisable(false);
        txtBuscarGrupo.setDisable(false);  // Enable group search in create mode
        txtBuscarVehiculo.setDisable(false);  // Enable vehicle search in create mode
        txtBuscarCodigoFabrica.setDisable(false);  // Enable factory code search in create mode

        btnNuevo.setDisable(true);
        btnGuardar.setDisable(false);
        btnCancelar.setDisable(false);

        setRelatedButtonsDisabled(false);
    }

    private void setRelatedButtonsDisabled(boolean disabled) {
        btnAgregarArchivo.setDisable(disabled);
    }

    private void showErrorMessage(String message) {
        runOnUiThread(() -> {
            progressIndicator.setVisible(false);
            alertUtil.showError(message);
        });
    }

    private void showSuccessMessage(String message) {
        runOnUiThread(() -> {
            progressIndicator.setVisible(false);
            alertUtil.showInfo("Éxito", message);
        });
    }

    /**
     * Opens the SearchProductGui dialog for advanced product search
     */
    private void openSearchProductGui() {
        if (searchProductGuiStage != null && searchProductGuiStage.isShowing()) {
            searchProductGuiStage.toFront();
            return;
        }

        try {
            // Load the SearchProductGui FXML
            javafx.scene.Parent searchProductGuiView = springFXMLLoader.load(FXMLEnum.SEARCH_PRODUCT);
            searchProductGuiController = springFXMLLoader.getController(searchProductGuiView);

            // Store the view for later use
            this.searchProductGuiView = searchProductGuiView;

            // Create and subscribe to a new SearchProductGui - use take(1) to get only the first emission
            // This prevents the timeout issue by completing the Flux after the first emission
            subscribeFluxWithUiUpdate(
                searchProductGuiService.createAndSubscribe().take(1),
                this::handleSearchProductGuiCreated,
                this::handleSearchProductGuiCreationError,
                Duration.ofSeconds(30) // 30 second timeout for SearchProductGui creation
            );

        } catch (Exception e) {
            log.error("Error al cargar SearchProductGui FXML: {}", e.getMessage(), e);
            showErrorMessage("Error al cargar la búsqueda avanzada: " + e.getMessage());
        }
    }

    /**
     * Handles the creation of a new SearchProductGui
     */
    private void handleSearchProductGuiCreated(corp.jamaro.jamaroescritoriofx.appfx.ventas.model.gui.SearchProductGui searchProductGui) {
        if (searchProductGui != null && searchProductGuiController != null && searchProductGuiView != null) {
            searchProductGuiId = searchProductGui.getId();

            // Set up the SearchProductGui controller
            searchProductGuiController.setSearchProductGuiId(searchProductGuiId);

            // Clear any existing context menu items to prevent duplication
            searchProductGuiController.clearContextMenuItems();

            // Configure context menu for product selection
            searchProductGuiController.addContextMenuItem(
                new SearchProductGuiController.ContextMenuItemConfig(
                    "Seleccionar Producto",
                    this::handleProductSelection,
                    true
                )
            );

            // Create and show the stage only after successful SearchProductGui creation
            searchProductGuiStage = new javafx.stage.Stage();
            searchProductGuiStage.setTitle("Búsqueda Avanzada de Productos");
            searchProductGuiStage.initModality(javafx.stage.Modality.WINDOW_MODAL);
            searchProductGuiStage.initOwner(btnBuscarProducto.getScene().getWindow());
            searchProductGuiStage.setScene(new javafx.scene.Scene(searchProductGuiView, 1000, 700));

            // Handle stage close event
            searchProductGuiStage.setOnCloseRequest(event -> closeSearchProductGui());

            searchProductGuiStage.show();

            log.info("SearchProductGui creado con ID: {}", searchProductGuiId);
        }
    }

    /**
     * Handles SearchProductGui creation errors
     */
    private void handleSearchProductGuiCreationError(Throwable error) {
        log.error("Error al crear SearchProductGui: {}", error.getMessage(), error);
        showErrorMessage("Error al crear la búsqueda avanzada: " + error.getMessage());
    }

    /**
     * Handles product selection from SearchProductGui
     */
    private void handleProductSelection(Producto producto) {
        if (producto != null && producto.getId() != null) {
            log.info("Producto seleccionado desde SearchProductGui: {}", producto.getCodProductoOld());

            // Load the selected product
            loadProductoById(producto.getId());

            // Close the SearchProductGui
            closeSearchProductGui();
        }
    }

    /**
     * Closes the SearchProductGui and cleans up resources
     */
    private void closeSearchProductGui() {
        if (searchProductGuiId != null) {
            // Close and delete the SearchProductGui on the server
            subscribeMonoWithUiUpdate(
                searchProductGuiService.closeAndDelete(searchProductGuiId),
                result -> log.info("SearchProductGui cerrado correctamente"),
                logAndShowError("cerrando SearchProductGui", this::showErrorMessage)
            );
            searchProductGuiId = null;
        }

        if (searchProductGuiController != null) {
            searchProductGuiController.onClose();
            searchProductGuiController = null;
        }

        if (searchProductGuiStage != null) {
            searchProductGuiStage.close();
            searchProductGuiStage = null;
        }

        // Clean up the stored view
        searchProductGuiView = null;
    }
}
